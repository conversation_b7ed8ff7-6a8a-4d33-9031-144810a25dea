
<template>
	<div class="p-6">
		<div class="max-w-4xl mx-auto">
			<h1 class="text-2xl font-bold mb-6">Settings</h1>

			<!-- Integrations Section -->
			<div class="space-y-6">
				<div class="bg-white rounded-lg shadow-sm p-6">
					<h2 class="text-lg font-semibold mb-4">Integrations</h2>
					<div class="grid gap-4">
						<div v-for="item in integrationItems">
							<div v-if="item.isVisible" class="border rounded-lg p-4 hover:bg-gray-50 cursor-pointer transition-colors"
								@click="item.deviceStatus?.isConnected ? () => { } : item.onClickConnect()">
								<div class="flex items-center justify-between">
									<div class="flex items-center gap-3">
										<div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
											<icon-device class="w-6 h-6 stroke-blue-600" />
										</div>
										<div>
											<h3 class="font-medium">{{ item.name }} Integration</h3>
											<div class="flex items-center gap-2">
												<div class="w-2 h-2 rounded-full"
													:class="item.deviceStatus?.isConnected ? 'bg-green-500' : 'bg-gray-300'">
												</div>
												<p class="text-sm text-gray-500">
													{{ item.deviceStatus?.isConnected
														? `Connected as ${item.deviceStatus?.username}`
														: 'Not connected' }}
												</p>
											</div>
										</div>
									</div>
									<div class="flex gap-2">
										<Button v-if="item.deviceStatus?.isConnected" variant="primary" label="Disconnect"
											@click.stop="() => { item.onClickDisconnect(); $storeMap.isReFetching = true; }" />
										<icon-chevron-right v-else class="w-5 h-5 stroke-gray-400" @click="item.onClickConnect" />
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- Telematics Modal -->
		<general-modal id="telematics-modal" title="Telematics Integration" is-has-close class-modal="max-w-md"
			@mounted="modalTelematics = $event">
			<template #body>
				<TelematicsLogin @success="handleTelematicsSuccess" />
			</template>
		</general-modal>

		<!-- MDVR Modal -->
		<general-modal id="mdvr-modal" title="MDVR Integration" :is-has-close="true" class-modal="max-w-md"
			@mounted="modalMDVR = $event">
			<template #body>
				<MDVRLogin @success="handleMDVRSuccess" />
			</template>
		</general-modal>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import TelematicsLogin from '~/components/integrations/TelematicsLogin.vue'
import MDVRLogin from '~/components/integrations/MDVRLogin.vue'
import iconDevice from '~/components/icon/Building.vue'
import iconChevronRight from '~/components/icon/ChevronRight.vue'
import Button from '~/components/general/Button.vue'
import type { ElementEvent } from '~/types/element'
import { useMapStore } from '~/store/map'

const { getSession } = useAuth()
const { initialProviders, isTelematics, isMdvr } = useProfile()
const $storeMap = useMapStore()

const modalTelematics = ref<ElementEvent | null>(null)
const modalMDVR = ref<ElementEvent | null>(null)

interface IntegrationStatus {
	isConnected: boolean;
	username?: string;
	expiry?: string;
}

const integrationStatuses = reactive<{ telematics: IntegrationStatus, mdvr: IntegrationStatus }>({
	telematics: { isConnected: false, username: undefined, expiry: undefined },
	mdvr: { isConnected: false, username: undefined, expiry: undefined }
})

const integrationItems = computed(() => {
	return [
		{
			name: 'Telematics',
			deviceStatus: { ...integrationStatuses.telematics },
			isVisible: !initialProviders.value.includes('telematics') && initialProviders.value.length > 0,
			onClickConnect: () => modalTelematics.value?.show(),
			onClickDisconnect: () => disconnectTelematics()
		},
		{
			name: 'MDVR',
			deviceStatus: { ...integrationStatuses.mdvr },
			isVisible: !initialProviders.value.includes('mdvr') && initialProviders.value.length > 0,
			onClickConnect: () => modalMDVR.value?.show(),
			onClickDisconnect: () => disconnectMDVR()

		}
	]
})

const fetchTelematicsStatus = async () => {
	try {
		const response = await $fetch('/api/telematics/token')
		integrationStatuses.telematics = response.data
	} catch (error) {
		console.error('Failed to fetch MDVR status:', error)
	}
}

const fetchMDVRStatus = async () => {
	try {
		const response = await $fetch('/api/mdvr/token')
		integrationStatuses.mdvr = response.data
	} catch (error) {
		console.error('Failed to fetch MDVR status:', error)
	}
}

const handleTelematicsSuccess = async () => {
	modalTelematics.value?.hide()
	await fetchTelematicsStatus()
	await getSession()
	$storeMap.isReFetching = true
}

const handleMDVRSuccess = async () => {
	modalMDVR.value?.hide()
	await fetchMDVRStatus()
	await getSession()
	$storeMap.isReFetching = true
}

const disconnectTelematics = async () => {
	try {
		await $fetch('/api/telematics/token', {
			method: 'DELETE'
		})
		await fetchTelematicsStatus()
		await getSession()
	} catch (error) {
		console.error('Failed to disconnect Telematics:', error)
	}
}

const disconnectMDVR = async () => {
	try {
		await $fetch('/api/mdvr/token', {
			method: 'DELETE'
		})
		await fetchMDVRStatus()
		await getSession()
	} catch (error) {
		console.error('Failed to disconnect MDVR:', error)
	}
}

onMounted(() => {
	Promise.all([
		fetchTelematicsStatus(),
		fetchMDVRStatus()
	])
})
</script>
