<script setup lang="ts">
import { ref, watch, onMounted, computed } from "vue";
import type { ElementEvent } from "~/types/element";
import IconCameraOff from "~/components/icon/CameraOff.vue";
import IconCube from "~/components/icon/Cube.vue";
import IconAlert from "~/components/icon/Alert.vue";
import IconUser from "~/components/icon/User.vue";
import IconMap from "~/components/icon/Map.vue";
import IconFileText from "~/components/icon/FileText.vue";
import { toast } from "vue3-toastify";
import { useFetch } from "#app";
import EvidenceMap from "./EvidenceMap.vue";

const props = defineProps({
  evidence: {
    type: Object,
    required: false,
    default: () => ({}),
  },
});

const emit = defineEmits(["on-mounted"]);
const modal = ref<ElementEvent | null>(null);
const selectedMediaIndex = ref(0);
const thumbnailUrls = ref<{ [key: string]: string }>({});

const getMediaFiles = () => {
  return props.evidence?.alarmFile || [];
};

const isImage = (file: any) => file.fileType === "4";
const isVideo = (file: any) => file.fileType === "2";

const loadThumbnails = async () => {
  const files = getMediaFiles();
  for (const file of files) {
    if (isVideo(file) && file.fileThumbnail) {
      const { data } = await useFetch("/api/mdvr/thumbnail", {
        query: {
          fileThumbnail: file.fileThumbnail,
          deviceID: file.deviceID || "",
          fileSize: file.fileSize || "",
          fileType: file.fileType || "",
          ac: file.ac || "",
        },
      });
      if (data.value?.status === 10000 && data.value?.data) {
        thumbnailUrls.value[file.fileGuid] = data.value.data;
      }
    }
  }
};

watch(
  () => props.evidence,
  () => {
    thumbnailUrls.value = {};
    loadThumbnails();
  },
  { deep: true }
);

onMounted(() => {
  loadThumbnails();
});

const downloadEvidence = (file: any) => {
  if (file?.downUrl) {
    window.open(file.downUrl, "_blank");
  } else {
    toast.error("No media available for download");
  }
};

const closeModal = () => {
  const modalElement = document.getElementById("modal-view-evidence");
  if (modalElement) {
    modalElement.classList.add("hidden");
    modalElement.classList.remove("animate-fade-in");
  }
};

const onModalMounted = (event: ElementEvent) => {
  modal.value = event;
  emit("on-mounted", event);
};

const coordinates = computed(() => {
  if (props.evidence?.alarmGps) {
    const [longitude, latitude] = props.evidence.alarmGps
      .split(",")
      .map(Number);
    return { latitude, longitude };
  }
  return null;
});
</script>

<template>
  <general-modal
    :id="`modal-view-evidence`"
    :title="
      props.evidence?.deviceName
        ? `Evidence Details - ${props.evidence.deviceName}`
        : 'Evidence Details'
    "
    classModal="max-w-2xl"
    size="lg"
    @on-mounted="onModalMounted"
  >
    <template #body>
      <div class="space-y-6">
        <!-- Media Gallery -->
        <div class="space-y-4">
          <!-- Main Media Display -->
          <div
            class="aspect-video bg-gray-100 rounded-lg flex items-center justify-center overflow-hidden"
          >
            <template v-if="getMediaFiles().length > 0">
              <NuxtImg
                v-if="isImage(getMediaFiles()[selectedMediaIndex])"
                :src="getMediaFiles()[selectedMediaIndex].downUrl"
                :alt="`Evidence ${selectedMediaIndex + 1}`"
                class="max-h-full object-contain"
              />
              <video
                v-else-if="isVideo(getMediaFiles()[selectedMediaIndex])"
                controls
                class="max-h-full w-full"
              >
                <source
                  :src="getMediaFiles()[selectedMediaIndex].downUrl"
                  type="video/mp4"
                />
                Your browser does not support the video tag.
              </video>
            </template>
            <div v-else class="text-gray-500">
              <icon-camera-off class="w-12 h-12 mx-auto mb-2" />
              <p>No media available</p>
            </div>
          </div>

          <!-- Thumbnails -->
          <div
            v-if="getMediaFiles().length > 1"
            class="flex gap-2 overflow-x-auto pb-2"
          >
            <button
              v-for="(file, index) in getMediaFiles()"
              :key="file.fileGuid"
              @click="selectedMediaIndex = index"
              class="relative flex-shrink-0 w-24 h-24 rounded-lg overflow-hidden focus:outline-none"
              :class="{ 'ring-2 ring-primary': selectedMediaIndex === index }"
            >
              <NuxtImg
                :src="
                  isVideo(file) ? thumbnailUrls[file.fileGuid] : file.downUrl
                "
                :alt="`Thumbnail ${index + 1}`"
                class="w-full h-full object-cover"
              />
              <div
                v-if="isVideo(file)"
                class="absolute inset-0 flex items-center justify-center bg-black/30"
              >
                <div class="bg-black/50 rounded-full p-2">
                  <svg
                    class="w-6 h-6 text-white"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"
                    />
                  </svg>
                </div>
              </div>
            </button>
          </div>

          <!-- Media Info -->
          <div class="text-sm text-gray-500 flex items-center justify-between">
            <div>
              <span class="font-medium">Channel:</span>
              {{ getMediaFiles()[selectedMediaIndex]?.channel || "N/A" }}
            </div>
            <div>
              <span class="font-medium">Time:</span>
              {{ getMediaFiles()[selectedMediaIndex]?.fileStartTime || "N/A" }}
            </div>
          </div>
        </div>
        <!-- Map Section -->
        <div v-if="coordinates" class="space-y-2">
          <h3 class="text-sm font-medium text-gray-700">Location</h3>
          <evidence-map
            :latitude="coordinates.latitude"
            :longitude="coordinates.longitude"
          />
        </div>

        <!-- Evidence Details -->
        <div class="space-y-4 bg-gray-50 p-4 rounded-lg">
          <h3 class="text-lg font-medium text-gray-800">Evidence Details</h3>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="bg-white p-4 rounded-lg shadow-sm">
              <p class="font-semibold text-gray-600 mb-3 flex items-center">
                <icon-cube class="w-4 h-4 mr-2" />
                Device Information
              </p>
              <div class="space-y-2">
                <general-label-data
                  label="Device Name"
                  :data="props.evidence?.deviceName ?? 'N/A'"
                />
                <general-label-data
                  label="Device ID"
                  :data="props.evidence?.deviceID ?? 'N/A'"
                />
                <general-label-data
                  label="Fleet Name"
                  :data="props.evidence?.fleetName ?? 'N/A'"
                />
              </div>
            </div>

            <div class="bg-white p-4 rounded-lg shadow-sm">
              <p class="font-semibold text-gray-600 mb-3 flex items-center">
                <icon-alert class="w-4 h-4 mr-2" />
                Alarm Information
              </p>
              <div class="space-y-2">
                <general-label-data
                  label="Alarm Type"
                  :data="props.evidence?.alarmTypeValue ?? 'N/A'"
                />
                <general-label-data
                  label="Alarm Time"
                  :data="
                    props.evidence?.alarmTime
                      ? new Date(props.evidence.alarmTime).toLocaleString()
                      : 'N/A'
                  "
                />
                <general-label-data
                  label="End Time"
                  :data="
                    props.evidence?.alarmTimeEnd
                      ? new Date(props.evidence.alarmTimeEnd).toLocaleString()
                      : 'N/A'
                  "
                />
                <general-label-data
                  label="Speed"
                  :data="
                    props.evidence?.speed
                      ? `${props.evidence.speed} Km/h`
                      : 'N/A'
                  "
                />
                <general-label-data
                  label="Alarm GUID"
                  :data="props.evidence?.alarmGuid ?? 'N/A'"
                />
              </div>
            </div>

            <div class="bg-white p-4 rounded-lg shadow-sm">
              <p class="font-semibold text-gray-600 mb-3 flex items-center">
                <icon-user class="w-4 h-4 mr-2" />
                Driver Information
              </p>
              <div class="space-y-2">
                <general-label-data
                  label="Driver Name"
                  :data="props.evidence?.driverName ?? 'N/A'"
                />
                <general-label-data
                  label="Driver License"
                  :data="props.evidence?.driverLicenseNo ?? 'N/A'"
                />
                <general-label-data
                  label="Card Number"
                  :data="props.evidence?.cardNo ?? 'N/A'"
                />
                <general-label-data
                  label="Contact Number"
                  :data="props.evidence?.tel ?? 'N/A'"
                />
              </div>
            </div>

            <div class="bg-white p-4 rounded-lg shadow-sm">
              <p class="font-semibold text-gray-600 mb-3 flex items-center">
                <icon-map class="w-4 h-4 mr-2" />
                Location Information
              </p>
              <div class="space-y-2">
                <general-label-data
                  label="Location"
                  :data="props.evidence?.location ?? 'N/A'"
                />
                <general-label-data
                  label="Start GPS"
                  :data="props.evidence?.alarmGps ?? 'N/A'"
                />
                <general-label-data
                  label="End GPS"
                  :data="props.evidence?.endGps ?? 'N/A'"
                />
                <general-label-data
                  label="Fence Name"
                  :data="props.evidence?.fenceName ?? 'N/A'"
                />
              </div>
            </div>
          </div>

          <div class="bg-white p-4 rounded-lg shadow-sm">
            <p class="font-semibold text-gray-600 mb-3 flex items-center">
              <icon-file-text class="w-4 h-4 mr-2" />
              Additional Information
            </p>
            <div class="space-y-2">
              <!-- <general-label-data label="Alarm Details" :data="props.evidence?.alarmValue ?? 'N/A'" /> -->
              <p class="text-sm text-gray-500">{{ props.label }}</p>

              <pre class="whitespace-pre-wrap text-sm bg-gray-50 p-2 rounded">{{
                props.evidence?.alarmValue || "N/A"
              }}</pre>
              <general-label-data
                label="Review Type"
                :data="props.evidence?.reviewType ?? 'N/A'"
              />
              <general-label-data
                label="Take Type"
                :data="props.evidence?.takeType ?? 'N/A'"
              />
              <general-label-data
                label="Take Up User"
                :data="props.evidence?.takeupUser ?? 'N/A'"
              />
              <general-label-data
                label="Take Up Time"
                :data="
                  props.evidence?.takeupTime
                    ? new Date(props.evidence.takeupTime).toLocaleString()
                    : 'N/A'
                "
              />
              <general-label-data
                label="Take Up Memo"
                :data="props.evidence?.takeupMemo ?? 'N/A'"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- Footer Actions -->
      <div class="flex justify-end gap-3 mt-6">
        <general-outlined-button label="Close" @on-click="closeModal" />
        <general-button
          label="Download Current"
          @on-click="downloadEvidence(getMediaFiles()[selectedMediaIndex])"
          :disabled="!getMediaFiles().length"
        />
      </div>
    </template>
  </general-modal>
</template>

<style scoped>
.overflow-x-auto {
  scrollbar-width: thin;
  scrollbar-color: theme("colors.gray.300") theme("colors.gray.100");
}
.overflow-x-auto::-webkit-scrollbar {
  height: 6px;
}
.overflow-x-auto::-webkit-scrollbar-track {
  background: theme("colors.gray.100");
  border-radius: 3px;
}
.overflow-x-auto::-webkit-scrollbar-thumb {
  background-color: theme("colors.gray.300");
  border-radius: 3px;
}
</style>
